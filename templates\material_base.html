{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="description" content="EcolePro est une application web progressive de gestion des écoles laïques et islamiques">
    <meta name="keywords" content="EcolePro, application, gestion, école, moyennes">
    <!-- Open Graph Meta-->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="EcolePro: Application de Gestion des Ecoles">
    <meta property="og:title" content="EcolePro: Gestion des Ecoles">
    <meta property="og:url" content="https://www.ecolepro.net/">
    <meta property="og:image" content="{% static 'img/favicon.png' %}">
    <meta property="og:description" content="EcolePro est une application de gestion des écoles en Côte d'Ivoire">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EcolePro | Gestion des écoles</title>
    
    <link rel="shortcut icon" href="{% static 'img/favicon.png' %}" type="image/x-icon">
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'material/css/styles.css' %}">
    <link rel="stylesheet" href="{% static 'material/css/matter.css' %}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>
<body x-data="minimalistApp()" hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'>
    <!-- Page Preloader -->
    <div class="page-preloader" id="page-preloader">
        <div class="preloader-content">
            <div class="mdc-circular-progress mdc-circular-progress--large mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading..." aria-valuemin="0" aria-valuemax="1" id="page-preloader-spinner">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="preloader-text">Loading...</div>
        </div>
    </div>

    <!-- Loading Overlay for UI Interactions -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="mdc-circular-progress mdc-circular-progress--medium" role="progressbar" aria-label="Processing..." aria-valuemin="0" aria-valuemax="1">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="16" cy="16" r="12.5" stroke-width="3"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="78.54" stroke-width="3"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="2.4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="16" cy="16" r="12.5" stroke-dasharray="78.54" stroke-dashoffset="39.27" stroke-width="3"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-text" id="loading-text">Processing...</div>
        </div>
    </div>

    <!-- App Bar -->
    <div class="app-bar">
        <span class="material-icons" id="menu-btn">menu</span>
        <h1 x-text="pageTitle">Dashboard</h1>
        <div class="actions">
            <span class="material-icons" id="dark-mode-toggle" title="Toggle Dark Mode"  @click="if(navigator.vibrate) navigator.vibrate(10);">dark_mode</span>
            <span class="material-icons" id="more-menu-btn">more_vert</span>
        </div>
    </div>
    
    {% include 'sidebar.html' %}
    
    <!-- Main Content -->
    <div class="main-content" id="main-content" hx-target="this">
        {% include template_name|default:'main.html' %}
    </div>

    <!-- Bottom App Bar (Mobile) -->
    <div class="bottom-app-bar" id="bottom-app-bar">
        <div class="bottom-nav-item" @click="setActiveNav('home')" :class="{ 'active': activeNav === 'home' }">
            <span class="material-icons">home</span>
            <span class="bottom-nav-label">Accueil</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('page1')" :class="{ 'active': activeNav === 'page1' }">
            <span class="material-icons">people</span>
            <span class="bottom-nav-label">Elèves</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('section1')" :class="{ 'active': activeNav === 'section1' }">
            <span class="material-icons">request_quote</span>
            <span class="bottom-nav-label">Paiements</span>
        </div>
        <div class="bottom-nav-item" @click="setActiveNav('section2')" :class="{ 'active': activeNav === 'section2' }">
            <span class="material-icons">leaderboard</span>
            <span class="bottom-nav-label">Notes et Résultats</span>
        </div>
    </div>

    {% include 'user_offcanvas.html' %}

    <!-- Sidebar Overlay for Mobile -->
    <div class="overlay" id="sidebar-overlay"></div>

    <!-- Modal Component -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal" id="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Modal Title</h3>
                <button class="modal-close" id="modal-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content" id="modal-content">
                <p>Modal content goes here...</p>
            </div>
        </div>
    </div>

    <!-- Sort Bottom Sheet -->
    <div class="bottom-sheet-overlay" id="sort-bottom-sheet-overlay">
        <div class="bottom-sheet" id="sort-bottom-sheet">
            <div class="bottom-sheet-handle"></div>
            <div class="bottom-sheet-header">
                <div class="bottom-sheet-title">Ordre d'affichage</div>
                <button class="bottom-sheet-close" id="sort-bottom-sheet-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="bottom-sheet-content">
                <div class="bottom-sheet-actions">
                    <button class="bottom-sheet-action mdc-ripple-surface" data-sort="student__last_name">
                        <span class="material-icons">sort_by_alpha</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Alphabétique (de A à Z)</div>
                        </div>
                    </button>
                    <button class="bottom-sheet-action mdc-ripple-surface" data-sort="-student__last_name">
                        <span class="material-icons">sort_by_alpha</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Alphabétique (de Z à A)</div>
                        </div>
                    </button>
                    <button class="bottom-sheet-action mdc-ripple-surface" data-sort="paid">
                        <span class="material-icons">trending_up</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Montant payé (croissant)</div>
                        </div>
                    </button>
                    <button class="bottom-sheet-action mdc-ripple-surface" data-sort="-paid">
                        <span class="material-icons">trending_down</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Montant payé (décroissant)</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Search Overlay -->
    <div class="mobile-search-overlay" id="mobile-search-overlay">
        <div class="mobile-search-container">
            <div class="mobile-search-header">
                <button class="mobile-search-back" id="mobile-search-back">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="mobile-search-input-container">
                    <input type="text" class="mobile-search-input" id="mobile-search-input"
                           placeholder="Rechercher des élèves..." autocomplete="off">
                </div>
            </div>
        </div>
        <div class="mobile-search-results" id="mobile-search-results">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <!-- Bottom Sheet -->
    <div class="bottom-sheet-overlay" id="bottom-sheet-overlay">
        <div class="bottom-sheet" id="bottom-sheet">
            <div class="bottom-sheet-handle"></div>
            <div class="bottom-sheet-header">
                <div class="bottom-sheet-title">Options de l'élève</div>
                <button class="bottom-sheet-close" id="bottom-sheet-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="bottom-sheet-content">
                <div class="student-preview" id="student-preview">
                    <div class="student-photo" id="preview-photo"></div>
                    <div class="student-info">
                        <div class="student-name" id="preview-name"></div>
                        <div class="student-details" id="preview-details"></div>
                    </div>
                </div>
                <div class="bottom-sheet-actions">
                    <button class="bottom-sheet-action mdc-ripple-surface" id="action-details">
                        <span class="material-icons">person</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Voir les détails</div>
                            <div class="bottom-sheet-action-subtitle">Informations complètes de l'élève</div>
                        </div>
                    </button>
                    <button class="bottom-sheet-action mdc-ripple-surface" id="action-edit"
                            hx-target="#modal-content">
                        <span class="material-icons">edit</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Modifier</div>
                            <div class="bottom-sheet-action-subtitle">Éditer les informations</div>
                        </div>
                    </button>
                    <button class="bottom-sheet-action mdc-ripple-surface" id="action-payment">
                        <span class="material-icons">payment</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Ajouter un paiement</div>
                            <div class="bottom-sheet-action-subtitle">Enregistrer un nouveau paiement</div>
                        </div>
                    </button>
                    <button class="bottom-sheet-action mdc-ripple-surface" id="action-photo">
                        <span class="material-icons">photo</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Changer photo</div>
                            <div class="bottom-sheet-action-subtitle">Prendre/Changer la photo</div>
                        </div>
                    </button>
                    <button class="bottom-sheet-action mdc-ripple-surface delete" id="action-delete">
                        <span class="material-icons">delete</span>
                        <div class="bottom-sheet-action-content">
                            <div class="bottom-sheet-action-title">Supprimer</div>
                            <div class="bottom-sheet-action-subtitle">Supprimer cet élève</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Code Scanner Modal -->
    <div class="modal-overlay" id="qr-scanner-overlay">
        <div class="modal qr-scanner-modal" id="qr-scanner-modal">
            <div class="modal-header">
                <h2 class="modal-title">Scanner QR Code</h2>
                <button class="modal-close" id="qr-scanner-close">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content qr-scanner-content">
                <div id="qr-reader"></div>
                <div class="qr-scanner-message" id="qr-scanner-message">
                    <div class="qr-scanner-icon">
                        <span class="material-icons">qr_code_scanner</span>
                    </div>
                    <div class="qr-scanner-text">Positionnez le code QR dans le cadre pour le scanner</div>
                </div>
                <div class="qr-scanner-result" id="qr-scanner-result" style="display: none;">
                    <div class="qr-result-icon">
                        <span class="material-icons">check_circle</span>
                    </div>
                    <div class="qr-result-title">Code QR scanné avec succès!</div>
                    <div class="qr-result-content" id="qr-result-content"></div>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="mdc-button mdc-button--outlined" id="qr-scanner-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Annuler</span>
                </button>
                <button type="button" class="mdc-button mdc-button--raised" id="qr-scanner-ok" style="display: none;">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">OK</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Snackbar Component -->
    <div class="mdc-snackbar" id="snackbar">
        <div class="mdc-snackbar__surface" role="status" aria-relevant="additions">
            <div class="mdc-snackbar__label" id="snackbar-label" aria-atomic="false">
                Message goes here
            </div>
            <div class="mdc-snackbar__actions" id="snackbar-actions">
                <button type="button" class="mdc-button mdc-snackbar__action" id="snackbar-action">
                    <div class="mdc-button__ripple"></div>
                    <span class="mdc-button__label">Action</span>
                </button>
                <button class="mdc-icon-button mdc-snackbar__dismiss material-icons" title="Dismiss" id="snackbar-dismiss">close</button>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="{% static 'js/htmx.min.js' %}"></script>
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>

    <!-- Flatpickr -->

    <!-- HTML5 QR Code Scanner -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'material/js/scripts.js' %}"></script>
    <script src="{% static 'material/js/charts.js' %}"></script>
    <script src="{% static 'material/js/modal-htmx.js' %}"></script>
    <script src="{% static 'material/js/mdc-auto-init.js' %}"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/fr.js" defer></script>
</body>
</html>
