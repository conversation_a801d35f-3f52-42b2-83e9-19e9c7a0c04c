{% load humanize %}
{% load static %}

<div class="content-header">
    <span class="material-icons">arrow_back</span>
    <h2 class="page-title">
        <span>Liste des élèves</span>
    </h2>
    <div class="actions">
        <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">add</span>
            <span class="mdc-button__label">Ajouter un élève</span>
        </button>
        <span class="material-icons hidden" title="Exporter vers Excel">description</span>
        <span class="material-icons hidden" title="Importer des données">file_upload</span>
        <span class="material-icons hidden" title="Changer de vue">view_module</span>
        <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn"
              onclick="if(typeof openMobileSearch === 'function') openMobileSearch(); else console.error('openMobileSearch not found');">search</span>
        <span class="material-icons" title="Trier" id="sort-btn" onclick="showSortBottomSheet()">sort_by_alpha</span>
        <span class="material-icons" title="Filtrer" id="filter-btn" onclick="showMobileFilterBottomSheet()">filter_list</span>
        <span class="material-icons" title="Vue liste / grille" id="filter-btn" hx-get="{{ request.path }}?view=grid">grid_view</span>
    </div>
</div>

<div class="content-area" id="content-area">
    <!-- Quick Filter Chips -->
    <div class="quick-filter-chips">
        <button class="quick-filter-chip {% if not 'filter_by' in request.GET or not request.GET.filter_by or request.GET.filter_by == 'all' %} active {% endif %}"
                data-filter="all"
                onclick="createRipple(event, this); changeFilter('all')">
            <span class="material-icons">group</span>
            <span>Tous</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'paid' %} active {% endif %}"
                data-filter="paid"
                onclick="createRipple(event, this); changeFilter('paid')">
            <span class="material-icons">paid</span>
            <span>Payé</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'unpaid' %} active {% endif %}"
                data-filter="unpaid"
                onclick="createRipple(event, this); changeFilter('unpaid')">
            <span class="material-icons">money_off</span>
            <span>Non payé</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'full_paid' %} active {% endif %}"
                data-filter="full_paid"
                onclick="createRipple(event, this); changeFilter('full_paid')">
            <span class="material-icons">check_circle</span>
            <span>Soldé</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'today' %} active {% endif %}"
                data-filter="today"
                onclick="createRipple(event, this); changeFilter('today')">
            <span class="material-icons">today</span>
            <span>Aujourd'hui</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'this_week' %} active {% endif %}"
                data-filter="this_week"
                onclick="createRipple(event, this); changeFilter('this_week')">
            <span class="material-icons">date_range</span>
            <span>Cette semaine</span>
        </button>
        <button class="quick-filter-chip {% if request.GET.filter_by == 'this_month' %} active {% endif %}"
                data-filter="this_month"
                onclick="createRipple(event, this); changeFilter('this_month')">
            <span class="material-icons">calendar_month</span>
            <span>Ce mois</span>
        </button>
    </div>

    <!-- Students List (Mobile) -->
    <div class="students-list" id="students-list">
        {% include 'student/partials/students_list_mobile_items.html' with enrollments=enrollments %}
        {% if not enrollments %}
            <!-- Empty State -->
            <div class="empty-state">
                <span class="material-icons">search_off</span>
                <div class="empty-text">Aucun élève trouvé</div>
            </div>
        {% endif %}
    </div>

    <!-- Loading Indicator -->
    <div class="infinite-scroll-loading" id="infinite-scroll-loading" style="display: none;">
        <div class="loading-spinner">
            <div class="mdc-circular-progress mdc-circular-progress--indeterminate">
                <div class="mdc-circular-progress__determinate-container">
                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                        <circle class="mdc-circular-progress__determinate-track" cx="24" cy="24" r="18" stroke-width="4"/>
                        <circle class="mdc-circular-progress__determinate-circle" cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="113.097" stroke-width="4"/>
                    </svg>
                </div>
                <div class="mdc-circular-progress__indeterminate-container">
                    <div class="mdc-circular-progress__spinner-layer">
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__gap-patch">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="3.2"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="24" cy="24" r="18" stroke-dasharray="113.097" stroke-dashoffset="56.549" stroke-width="4"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="loading-text">Chargement...</div>
    </div>

    <!-- End of List Indicator -->
    <div class="end-of-list" id="end-of-list" style="display: none;">
        <div class="end-of-list-content">
            <span class="material-icons">check_circle</span>
            <div class="end-of-list-text">Fin de la liste</div>
        </div>
    </div>


</div>

<!-- QR Code Floating Action Button -->
<button class="mdc-fab qr-fab" id="qr-fab" title="Scanner QR Code">
    <div class="mdc-fab__ripple"></div>
    <span class="material-icons mdc-fab__icon">qr_code</span>
</button>

<!-- Add Student Floating Action Button -->
<button class="mdc-fab mdc-fab--extended" id="add-student-fab" title="Ajouter un élève"
        hx-get="{% url 'school:student_add_wizard' %}"
        hx-target="#modal-content"
        hx-trigger="click"
        hx-swap="innerHTML">
    <div class="mdc-fab__ripple"></div>
    <span class="material-icons mdc-fab__icon">add</span>
    <span class="mdc-fab__label">Ajouter un élève</span>
</button>

<style>
/* Student Card Styles */
.student-photo {
    width: 60px !important;
    height: 60px !important;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
}

.student-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.student-name {
    font-weight: 600;
    color: var(--text-primary, #333);
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.student-id {
    color: var(--text-primary, #333);
    font-size: 14px;
    font-weight: 500;
    margin-left: 12px;
}

/* Student Info Row Styles */
.student-info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
    gap: 12px;
    padding: 1px 0;
}

.info-label {
    color: var(--text-secondary, #666);
    font-size: 13px;
    font-weight: 500;
    flex-shrink: 0;
    min-width: 80px;
    line-height: 1.3;
}

.info-content {
    color: var(--text-primary, #333);
    font-size: 13px;
    text-align: right;
    flex: 1;
    word-break: break-word;
    line-height: 1.3;
}

/* Payment specific styling */
.payment-info .payment-label {
    color: var(--text-secondary, #666);
    font-size: 12px;
    font-weight: 500;
}

/* Level specific styling */
.level-info .level-label,
.dot-seperator {
    color: var(--text-secondary, #666);
    font-size: 12px;
    font-weight: 500;
}

/* Infinite Scroll Styles */
.infinite-scroll-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px;
    gap: 12px;
}

.loading-spinner .mdc-circular-progress {
    width: 32px;
    height: 32px;
    color: var(--primary-color, #4CAF50);
}

.loading-text {
    color: var(--text-secondary, #666);
    font-size: 14px;
}

.end-of-list {
    display: flex;
    justify-content: center;
    padding: 32px 16px;
}

.end-of-list-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary, #666);
}

.end-of-list-content .material-icons {
    font-size: 32px;
    color: var(--primary-color, #4CAF50);
}

.end-of-list-text {
    font-size: 14px;
    font-weight: 500;
}

.loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 48px 16px;
    color: var(--text-secondary, #666);
    font-size: 14px;
}
</style>

<!-- <script src="{% static 'material/js/table-row-selection.js' %}"></script> -->
<!-- <script src="{% static 'material/js/floating-actions.js' %}"></script> -->
<script>
// Infinite scroll variables - use window object to avoid redeclaration
window.studentsListInfiniteScroll = window.studentsListInfiniteScroll || {
    currentPage: 2,
    isLoading: false,
    hasMoreData: true,
    currentFilter: '{{ request.GET.filter_by|default:"all" }}',
    currentSort: '{{ request.GET.sort|default:"" }}'
};

// Initialize mobile search when this template loads
document.addEventListener('DOMContentLoaded', function() {
    // Ensure mobile search is initialized
    if (typeof initializeMobileSearch === 'function') {
        initializeMobileSearch();
    }

    // Initialize infinite scroll
    initializeInfiniteScroll();

    // Initialize sort bottom sheet
    initializeSortBottomSheet();

    // Set initial sort icon based on current sort
    updateSortIcon(window.studentsListInfiniteScroll.currentSort);

    // Initialize mobile filter bottom sheet
    initializeMobileFilterBottomSheet();
});

// Also initialize when HTMX loads this content
document.body.addEventListener('htmx:afterSwap', function(event) {
    // Check if this swap included the mobile search button
    if (event.detail.target.querySelector && event.detail.target.querySelector('#mobile-search-btn')) {
        if (typeof initializeMobileSearch === 'function') {
            initializeMobileSearch();
        }
    }

    // Reinitialize infinite scroll if content area was swapped
    if (event.detail.target.id === 'content-area') {
        window.studentsListInfiniteScroll.currentPage = 2;
        window.studentsListInfiniteScroll.isLoading = false;
        window.studentsListInfiniteScroll.hasMoreData = true;
        window.studentsListInfiniteScroll.currentFilter = new URLSearchParams(window.location.search).get('filter_by') || 'all';
        initializeInfiniteScroll();
    }
});

function initializeInfiniteScroll() {
    const contentArea = document.getElementById('content-area');

    // Remove existing scroll listeners
    if (contentArea) {
        contentArea.removeEventListener('scroll', throttledHandleScroll);
    }
    window.removeEventListener('scroll', throttledHandleWindowScroll);

    // Add throttled scroll listeners for better performance
    if (contentArea) {
        contentArea.addEventListener('scroll', throttledHandleScroll, { passive: true });
    }
    window.addEventListener('scroll', throttledHandleWindowScroll, { passive: true });

    // Also add touch events for mobile
    if (contentArea) {
        contentArea.addEventListener('touchmove', throttledHandleScroll, { passive: true });
    }
    document.addEventListener('touchmove', throttledHandleWindowScroll, { passive: true });
}

// Throttle function to limit how often scroll events fire
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

function handleScroll(event) {
    if (window.studentsListInfiniteScroll.isLoading || !window.studentsListInfiniteScroll.hasMoreData) return;

    const target = event.target;
    const scrollTop = target.scrollTop;
    const scrollHeight = target.scrollHeight;
    const clientHeight = target.clientHeight;

    // More aggressive trigger - load when user is 600px from bottom or 80% scrolled
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

    if (distanceFromBottom <= 100 || scrollPercentage >= 0.2) {
        loadMoreStudents();
    }
}

function handleWindowScroll() {
    if (window.studentsListInfiniteScroll.isLoading || !window.studentsListInfiniteScroll.hasMoreData) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight;
    const clientHeight = window.innerHeight;

    // More aggressive trigger - load when user is 600px from bottom or 80% scrolled
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

    if (distanceFromBottom <= 100 || scrollPercentage >= 0.2) {
        loadMoreStudents();
    }
}

// Create throttled versions of scroll handlers
const throttledHandleScroll = throttle(handleScroll, 100);
const throttledHandleWindowScroll = throttle(handleWindowScroll, 100);

function loadMoreStudents() {
    if (window.studentsListInfiniteScroll.isLoading || !window.studentsListInfiniteScroll.hasMoreData) return;

    window.studentsListInfiniteScroll.isLoading = true;

    // Show loading indicator
    const loadingIndicator = document.getElementById('infinite-scroll-loading');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'flex';
    }

    // Build URL with current filter and sort
    const url = new URL(window.location.href);
    url.searchParams.set('infinite_scroll', 'true');
    url.searchParams.set('page', window.studentsListInfiniteScroll.currentPage);
    url.searchParams.set('filter_by', window.studentsListInfiniteScroll.currentFilter);
    if (window.studentsListInfiniteScroll.currentSort) {
        url.searchParams.set('sort', window.studentsListInfiniteScroll.currentSort);
    }

    fetch(url.toString())
        .then(response => response.json())
        .then(data => {
            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }

            if (data.html && data.html.trim()) {
                // Append new items to the list
                const studentsList = document.getElementById('students-list');
                if (studentsList) {
                    studentsList.insertAdjacentHTML('beforeend', data.html);

                    // Reinitialize any components for new items
                    if (typeof initializeStudentCardHandlers === 'function') {
                        initializeStudentCardHandlers();
                    }

                    // Initialize ripple effects for new items
                    initializeRippleEffects();
                }

                // Update page counter
                window.studentsListInfiniteScroll.currentPage = data.page;
                window.studentsListInfiniteScroll.hasMoreData = data.has_more;
            } else {
                window.studentsListInfiniteScroll.hasMoreData = false;
            }

            // Show end of list indicator if no more data
            if (!window.studentsListInfiniteScroll.hasMoreData) {
                const endOfList = document.getElementById('end-of-list');
                if (endOfList) {
                    endOfList.style.display = 'flex';
                }
            }

            window.studentsListInfiniteScroll.isLoading = false;
        })
        .catch(error => {
            console.error('Error loading more students:', error);

            // Hide loading indicator
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }

            window.studentsListInfiniteScroll.isLoading = false;
        });
}

function changeFilter(filterValue) {
    // Update active chip
    document.querySelectorAll('.quick-filter-chip').forEach(chip => {
        chip.classList.remove('active');
    });
    document.querySelector(`[data-filter="${filterValue}"]`).classList.add('active');

    // Update URL
    const url = new URL(window.location.href);
    if (filterValue === 'all') {
        url.searchParams.delete('filter_by');
    } else {
        url.searchParams.set('filter_by', filterValue);
    }
    // Preserve current sort
    if (window.studentsListInfiniteScroll.currentSort) {
        url.searchParams.set('sort', window.studentsListInfiniteScroll.currentSort);
    }
    window.history.pushState({}, '', url.toString());

    // Reset infinite scroll state
    window.studentsListInfiniteScroll.currentPage = 2;
    window.studentsListInfiniteScroll.isLoading = false;
    window.studentsListInfiniteScroll.hasMoreData = true;
    window.studentsListInfiniteScroll.currentFilter = filterValue;

    // Hide end of list indicator
    const endOfList = document.getElementById('end-of-list');
    if (endOfList) {
        endOfList.style.display = 'none';
    }

    // Load new filtered content
    loadFilteredContent(filterValue);
}

function loadFilteredContent(filterValue) {
    // Show loading state
    const studentsList = document.getElementById('students-list');
    if (studentsList) {
        studentsList.innerHTML = '<div class="loading-placeholder">Chargement...</div>';
    }

    // Build URL for first page with filter and current sort
    const url = new URL(window.location.href);
    url.searchParams.set('filter_by', filterValue);
    if (window.studentsListInfiniteScroll.currentSort) {
        url.searchParams.set('sort', window.studentsListInfiniteScroll.currentSort);
    }

    // Use HTMX to load filtered content
    htmx.ajax('GET', url.toString(), {
        target: '#content-area',
        swap: 'innerHTML'
    });

    // Delete the first .content-header
    const contentHeader = document.querySelector('.content-header');
    if (contentHeader) {
        contentHeader.remove();
    }
}

// Sort Bottom Sheet Functions
function showSortBottomSheet() {
    const sortOverlay = document.getElementById('sort-bottom-sheet-overlay');
    const sortSheet = document.getElementById('sort-bottom-sheet');

    if (sortOverlay && sortSheet) {
        sortOverlay.classList.add('active');
        sortSheet.classList.add('active');
    }
}

function hideSortBottomSheet() {
    const sortOverlay = document.getElementById('sort-bottom-sheet-overlay');
    const sortSheet = document.getElementById('sort-bottom-sheet');

    if (sortOverlay && sortSheet) {
        sortOverlay.classList.remove('active');
        sortSheet.classList.remove('active');
    }
}

function changeSort(sortValue) {
    // Update current sort
    window.studentsListInfiniteScroll.currentSort = sortValue;

    // Update sort icon based on selection
    updateSortIcon(sortValue);

    // Update URL
    const url = new URL(window.location.href);
    if (sortValue) {
        url.searchParams.set('sort', sortValue);
    } else {
        url.searchParams.delete('sort');
    }

    // Update browser history
    window.history.pushState({}, '', url.toString());

    // Load sorted content
    loadSortedContent(sortValue);

    // Hide sort bottom sheet
    hideSortBottomSheet();
}

function updateSortIcon(sortValue) {
    const sortBtn = document.getElementById('sort-btn');
    if (!sortBtn) return;

    // Map sort values to icons
    const iconMap = {
        'student__last_name': 'sort_by_alpha',
        '-created_at': 'schedule',
        'paid': 'trending_up',
        '-paid': 'trending_down'
    };

    // Update icon based on sort value, default to sort_by_alpha
    const newIcon = iconMap[sortValue] || 'sort_by_alpha';
    sortBtn.textContent = newIcon;
}

function loadSortedContent(sortValue) {
    // Show loading state
    const studentsList = document.getElementById('students-list');
    if (studentsList) {
        studentsList.innerHTML = '<div class="loading-placeholder">Chargement...</div>';
    }

    // Build URL for first page with current filter and sort
    const url = new URL(window.location.href);
    url.searchParams.set('filter_by', window.studentsListInfiniteScroll.currentFilter);
    if (sortValue) {
        url.searchParams.set('sort', sortValue);
    } else {
        url.searchParams.delete('sort');
    }

    // Use HTMX to load sorted content
    htmx.ajax('GET', url.toString(), {
        target: '#content-area',
        swap: 'innerHTML'
    });

    // Delete the first .content-header
    const contentHeader = document.querySelector('.content-header');
    if (contentHeader) {
        contentHeader.remove();
    }

    // Reset infinite scroll
    window.studentsListInfiniteScroll.currentPage = 2;
    window.studentsListInfiniteScroll.hasMoreData = true;
}

function initializeSortBottomSheet() {
    // Add event listeners for sort bottom sheet
    const sortOverlay = document.getElementById('sort-bottom-sheet-overlay');
    const sortClose = document.getElementById('sort-bottom-sheet-close');
    const sortActions = document.querySelectorAll('#sort-bottom-sheet .bottom-sheet-action[data-sort]');

    // Close on overlay click
    if (sortOverlay) {
        sortOverlay.addEventListener('click', function(e) {
            if (e.target === sortOverlay) {
                hideSortBottomSheet();
            }
        });
    }

    // Close on close button click
    if (sortClose) {
        sortClose.addEventListener('click', hideSortBottomSheet);
    }

    // Handle sort option clicks
    sortActions.forEach(action => {
        action.addEventListener('click', function() {
            const sortValue = this.getAttribute('data-sort');
            changeSort(sortValue);
        });
    });

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const sortOverlay = document.getElementById('sort-bottom-sheet-overlay');
            if (sortOverlay && sortOverlay.classList.contains('active')) {
                hideSortBottomSheet();
            }
        }
    });
}

// Initialize ripple effects
function initializeRippleEffects() {
    const rippleElements = document.querySelectorAll('.mdc-ripple-surface:not(.mdc-ripple-upgraded)');
    rippleElements.forEach(element => {
        if (typeof mdc !== 'undefined' && mdc.ripple) {
            mdc.ripple.MDCRipple.attachTo(element);
        }
    });
}

// Immediate initialization for current page
if (typeof initializeMobileSearch === 'function') {
    initializeMobileSearch();
}
if (typeof initializeBottomSheet === 'function') {
    initializeBottomSheet();
}
if (typeof initializeFloatingActionButtons === 'function') {
    initializeFloatingActionButtons();
}
if (typeof initializeStudentCardHandlers === 'function') {
    initializeStudentCardHandlers();
}

// Initialize ripple effects
initializeRippleEffects();

// Mobile Filter Bottom Sheet Functions
let mobileFilterSelects = {};
let currentMobileFilters = {
    search: '',
    gender: '',
    birth_year: ''
};

function initializeMobileFilterBottomSheet() {
    // Initialize current filter values from URL
    const urlParams = new URLSearchParams(window.location.search);
    currentMobileFilters.search = urlParams.get('search') || '';
    currentMobileFilters.gender = urlParams.get('gender') || '';
    currentMobileFilters.birth_year = urlParams.get('birth_year') || '';

    // Populate birth year options
    populateBirthYearOptions();
}

function populateBirthYearOptions() {
    const birthYearList = document.querySelector('#mobile-birth-year-select .mdc-deprecated-list');
    if (birthYearList) {
        const currentYear = new Date().getFullYear();
        for (let year = currentYear - 2; year >= 1980; year--) {
            const li = document.createElement('li');
            li.className = 'mdc-deprecated-list-item';
            li.setAttribute('data-value', year);
            li.setAttribute('role', 'option');
            li.innerHTML = `
                <span class="mdc-deprecated-list-item__ripple"></span>
                <span class="mdc-deprecated-list-item__text">${year}</span>
            `;
            birthYearList.appendChild(li);
        }
    }
}

function showMobileFilterBottomSheet() {
    const overlay = document.getElementById('filter-bottom-sheet-overlay');
    const bottomSheet = document.getElementById('filter-bottom-sheet');

    if (overlay && bottomSheet) {
        overlay.classList.add('active');
        bottomSheet.classList.add('active');

        // Initialize Material Design components
        initializeMobileFilterComponents();

        // Set current values
        updateMobileFilterValues();

        // Add event listeners
        addMobileFilterEventListeners();
    }
}

function hideMobileFilterBottomSheet() {
    const overlay = document.getElementById('filter-bottom-sheet-overlay');
    const bottomSheet = document.getElementById('filter-bottom-sheet');

    if (overlay && bottomSheet) {
        overlay.classList.remove('active');
        bottomSheet.classList.remove('active');
    }
}

function initializeMobileFilterComponents() {
    // Initialize Material Design text field for search
    const searchField = document.querySelector('#mobile-search-filter').closest('.mdc-text-field');
    if (searchField && typeof mdc !== 'undefined') {
        new mdc.textField.MDCTextField(searchField);
    }

    // Initialize Material Design select components
    const selectElements = ['mobile-gender-select', 'mobile-birth-year-select'];
    selectElements.forEach(id => {
        const selectElement = document.getElementById(id);
        if (selectElement && typeof mdc !== 'undefined') {
            mobileFilterSelects[id] = new mdc.select.MDCSelect(selectElement);
        }
    });
}

function updateMobileFilterValues() {
    // Update search input
    const searchInput = document.getElementById('mobile-search-filter');
    if (searchInput) {
        searchInput.value = currentMobileFilters.search;
    }

    // Update select components
    if (mobileFilterSelects['mobile-gender-select']) {
        mobileFilterSelects['mobile-gender-select'].value = currentMobileFilters.gender;
    }

    if (mobileFilterSelects['mobile-birth-year-select']) {
        mobileFilterSelects['mobile-birth-year-select'].value = currentMobileFilters.birth_year;
    }
}

function addMobileFilterEventListeners() {
    // Close button
    const closeBtn = document.getElementById('filter-bottom-sheet-close');
    if (closeBtn) {
        closeBtn.replaceWith(closeBtn.cloneNode(true));
        document.getElementById('filter-bottom-sheet-close').addEventListener('click', hideMobileFilterBottomSheet);
    }

    // Overlay click
    const overlay = document.getElementById('filter-bottom-sheet-overlay');
    if (overlay) {
        overlay.replaceWith(overlay.cloneNode(true));
        document.getElementById('filter-bottom-sheet-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                hideMobileFilterBottomSheet();
            }
        });
    }

    // Clear filters button
    const clearBtn = document.getElementById('mobile-clear-filters-btn');
    if (clearBtn) {
        clearBtn.replaceWith(clearBtn.cloneNode(true));
        document.getElementById('mobile-clear-filters-btn').addEventListener('click', function() {
            clearMobileFilters();
        });
    }

    // Apply filters button
    const applyBtn = document.getElementById('mobile-apply-filters-btn');
    if (applyBtn) {
        applyBtn.replaceWith(applyBtn.cloneNode(true));
        document.getElementById('mobile-apply-filters-btn').addEventListener('click', function() {
            applyMobileFilters();
            hideMobileFilterBottomSheet();
        });
    }
}

function clearMobileFilters() {
    currentMobileFilters = {
        search: '',
        gender: '',
        birth_year: ''
    };
    updateMobileFilterValues();
}

function applyMobileFilters() {
    // Get current values from form
    const searchInput = document.getElementById('mobile-search-filter');
    if (searchInput) {
        currentMobileFilters.search = searchInput.value;
    }

    if (mobileFilterSelects['mobile-gender-select']) {
        currentMobileFilters.gender = mobileFilterSelects['mobile-gender-select'].value;
    }

    if (mobileFilterSelects['mobile-birth-year-select']) {
        currentMobileFilters.birth_year = mobileFilterSelects['mobile-birth-year-select'].value;
    }

    // Build URL with filters
    const url = new URL(window.location.href);

    // Clear existing filter parameters
    url.searchParams.delete('search');
    url.searchParams.delete('gender');
    url.searchParams.delete('birth_year');

    // Apply new filters
    if (currentMobileFilters.search) {
        url.searchParams.set('search', currentMobileFilters.search);
    }
    if (currentMobileFilters.gender) {
        url.searchParams.set('gender', currentMobileFilters.gender);
    }
    if (currentMobileFilters.birth_year) {
        url.searchParams.set('birth_year', currentMobileFilters.birth_year);
    }

    // Preserve current sort and filter_by
    if (window.studentsListInfiniteScroll.currentSort) {
        url.searchParams.set('sort', window.studentsListInfiniteScroll.currentSort);
    }
    if (window.studentsListInfiniteScroll.currentFilter && window.studentsListInfiniteScroll.currentFilter !== 'all') {
        url.searchParams.set('filter_by', window.studentsListInfiniteScroll.currentFilter);
    }

    // Update browser history
    window.history.pushState({}, '', url.toString());

    // Load filtered content
    loadMobileFilteredContent();
}

function loadMobileFilteredContent() {
    // Show loading state
    const studentsList = document.getElementById('students-list');
    if (studentsList) {
        studentsList.innerHTML = '<div class="loading-placeholder">Chargement...</div>';
    }

    // Use HTMX to load filtered content
    htmx.ajax('GET', window.location.href, {
        target: '#content-area',
        swap: 'innerHTML'
    });
}
</script>
